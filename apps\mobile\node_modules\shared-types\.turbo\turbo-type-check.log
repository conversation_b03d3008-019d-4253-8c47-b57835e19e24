[?9001h[?1004h[?25l[2J[m[H]0;C:\WINDOWS\system32\cmd.exe [?25h[?25l
> shared-types@0.1.0 type-check C:\Users\<USER>\Desktop\tap2go\packages\shared-types
> tsc --noEmit[5;1H[?25h[?25l[96m../shared-ui/src/components.ts[m:[93m21[m:[93m7 [m- [91merror [90mTS1005: [m'>' expected.[7m[7;1H21[27m       className={`animate-spin rounded-full border-2 border-gray-300 ${sizeClasses[size]} ${className}`}
[?25h
[7m  [27m       [91m~~~~~~~~~[K
[m

[96m../shared-ui/src/components.ts[m:[93m21[m:[93m16 [m- [91merror [90mTS1005: [m')' expected.

[7m21[27m       className={`animate-spin rounded-full border-2 border-gray-300 ${sizeClass
[7;83Hses[size]} ${className}`}
[7m  [27m[16X[91m[16C~[K
[m

[96m../shared-ui/src/components.ts[m:[93m21[m:[93m18 [m- [91merror [90mTS1136: [mProperty assignment expected. [8;1H

[7m21[27m       className={`animate-spin rounded-full border-2 border-gray-300 ${sizeClass
[7;83Hses[size]} ${className}`}
[7m  [27m[18X[91m[18C~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[K
[m

[96m../shared-ui/src/components.ts[m:[93m22[m:[93m14 [m- [91merror [90mTS1136: [mProperty assignment expected. [8;1H

[7m22[27m       style={{ borderTopColor: '#f3a823' }}[K
[7m  [27m[14X[91m[14C~[K
[m

[96m../shared-ui/src/components.ts[m:[93m22[m:[93m43 [m- [91merror [90mTS1128: [mDeclaration or statement expec
[7;83Hcted.

[7m22[27m       style={{ borderTopColor: '#f3a823' }}[K
[7m  [27m[43X[91m[43C~[K
[m

[96m../shared-ui/src/components.ts[m:[93m23[m:[93m5 [m- [91merror [90mTS1161: [mUnterminated regular expression
[7;83Hn literal.

[7m23[27m     />[K
[7m  [27m     [91m~~[K
[m

[96m../shared-ui/src/components.ts[m:[93m24[m:[93m3 [m- [91merror [90mTS1128: [mDeclaration or statement expect
[7;83Hted.

[7m24[27m   );[K
[7m  [27m   [91m~[K
[m

[96m../shared-ui/src/components.ts[m:[93m25[m:[93m1 [m- [91merror [90mTS1128: [mDeclaration or statement expect
[7;83Hted.

[7m25[27m };[K
[7m  [27m [91m~[K
[m

[96m../shared-ui/src/components.ts[m:[93m58[m:[93m7 [m- [91merror [90mTS1005: [m'>' expected.[K

[7m58[27m       className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}
[7;83H} ${className}`}
[7m  [27m       [91m~~~~~~~~~[K
[m

[96m../shared-ui/src/components.ts[m:[93m58[m:[93m16 [m- [91merror [90mTS1005: [m')' expected.[K

[7m58[27m       className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}
[7;83H} ${className}`}
[7m  [27m[16X[91m[16C~[K
[m

[96m../shared-ui/src/components.ts[m:[93m58[m:[93m18 [m- [91merror [90mTS1136: [mProperty assignment expected. [8;1H

[7m58[27m       className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}
[7;83H} ${className}`}
[7m  [27m[18X[91m[18C~~~[K
[m

[96m../shared-ui/src/components.ts[m:[93m59[m:[93m8 [m- [91merror [90mTS1128: [mDeclaration or statement expect
[7;83Hted.

[7m59[27m       {...props}[K
[7m  [27m        [91m~~~[K
[m

[96m../shared-ui/src/components.ts[m:[93m60[m:[93m5 [m- [91merror [90mTS1109: [mExpression expected.[K

[7m60[27m     >[K
[7m  [27m     [91m~[K
[m

[96m../shared-ui/src/components.ts[m:[93m62[m:[93m6 [m- [91merror [90mTS1161: [mUnterminated regular expression
[7;83Hn literal.

[7m62[27m     </button>[K
[7m  [27m      [91m~~~~~~~~[K
[m

[96m../shared-ui/src/components.ts[m:[93m63[m:[93m3 [m- [91merror [90mTS1128: [mDeclaration or statement expect
[7;83Hted.

[7m63[27m   );[K
[7m  [27m   [91m~[K
[m

[96m../shared-ui/src/components.ts[m:[93m64[m:[93m1 [m- [91merror [90mTS1128: [mDeclaration or statement expect
[7;83Hted.

[7m64[27m };[K
[7m  [27m [91m~[K
[m


Found 16 errors in the same file, starting at: ../shared-ui/src/components.ts[90m:21   [8;1H[m

[30m[41m ELIFECYCLE [m [31mCommand failed with exit code 2.
[m
[?9001l[?1004l
