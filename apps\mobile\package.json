{"name": "mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"dev": "expo start", "start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-firebase/firestore": "^22.2.1", "@react-navigation/bottom-tabs": "^7.3.17", "@react-navigation/native": "^7.1.13", "@react-navigation/stack": "^7.3.6", "api-client": "workspace:^", "business-logic": "workspace:^", "expo": "~53.0.12", "expo-linear-gradient": "~14.1.5", "expo-router": "^5.1.0", "expo-status-bar": "~2.2.3", "fbjs": "^3.0.5", "firebase": "^11.8.1", "firebase-config": "workspace:^", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.4", "react-native-maps": "^1.24.3", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-web-linear-gradient": "^1.1.2", "shared-types": "workspace:^", "shared-ui": "workspace:^"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}